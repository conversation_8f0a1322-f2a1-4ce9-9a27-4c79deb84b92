import React, { createContext, useState, useContext, ReactNode, useCallback } from 'react';
import { Invoice, InvoiceStatus } from '../types';
import { MOCK_INVOICES } from '../data/mockData';

interface InvoiceContextType {
  invoices: Invoice[];
  getInvoice: (id: string) => Invoice | undefined;
  addInvoice: (invoice: Omit<Invoice, 'id' | 'invoiceNumber'>) => void;
  updateInvoice: (id: string, updatedInvoice: Invoice) => void;
  updateInvoiceStatus: (id: string, status: InvoiceStatus) => void;
  deleteInvoice: (id: string) => void;
}

const InvoiceContext = createContext<InvoiceContextType | undefined>(undefined);

export const InvoiceProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [invoices, setInvoices] = useState<Invoice[]>(MOCK_INVOICES);

  const getInvoice = useCallback((id: string) => {
    return invoices.find(inv => inv.id === id);
  }, [invoices]);

  const addInvoice = (invoiceData: Omit<Invoice, 'id'| 'invoiceNumber'>) => {
    const newInvoiceNumber = `2024-${(invoices.length + 1).toString().padStart(3, '0')}`;
    const newInvoice: Invoice = {
      ...invoiceData,
      id: `inv-${Date.now()}`,
      invoiceNumber: newInvoiceNumber,
    };
    setInvoices(prev => [newInvoice, ...prev]);
  };

  const updateInvoice = (id: string, updatedInvoice: Invoice) => {
    setInvoices(prev => prev.map(inv => (inv.id === id ? updatedInvoice : inv)));
  };

  const updateInvoiceStatus = (id: string, status: InvoiceStatus) => {
    setInvoices(prev =>
      prev.map(inv => (inv.id === id ? { ...inv, status } : inv))
    );
  };
  
  const deleteInvoice = (id: string) => {
    setInvoices(prev => prev.filter(inv => inv.id !== id));
  };

  const value = { invoices, getInvoice, addInvoice, updateInvoice, updateInvoiceStatus, deleteInvoice };

  return <InvoiceContext.Provider value={value}>{children}</InvoiceContext.Provider>;
};

export const useInvoices = (): InvoiceContextType => {
  const context = useContext(InvoiceContext);
  if (!context) {
    throw new Error('useInvoices doit être utilisé au sein d\'un InvoiceProvider');
  }
  return context;
};