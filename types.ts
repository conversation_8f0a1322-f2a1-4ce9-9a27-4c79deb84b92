
export enum InvoiceStatus {
  Draft = 'Brouillon',
  Sent = 'Envoyée',
  Paid = 'Payée',
  Overdue = 'En retard',
}

export interface LineItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
}

export interface Client {
  name: string;
  address: string;
  email: string;
}

export interface Invoice {
  id: string;
  client: Client;
  invoiceNumber: string;
  issueDate: string;
  dueDate: string;
  items: LineItem[];
  status: InvoiceStatus;
}
