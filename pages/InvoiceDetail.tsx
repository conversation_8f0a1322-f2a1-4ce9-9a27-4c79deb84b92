
import React from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { useInvoices } from '../context/InvoiceContext';
import { InvoiceStatus } from '../types';
import { PencilIcon } from '../components/Icons';

const InvoiceDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { getInvoice, updateInvoiceStatus } = useInvoices();
  const invoice = id ? getInvoice(id) : undefined;

  if (!invoice) {
    return (
      <div className="text-center p-10">
        <h2 className="text-2xl text-white">Facture non trouvée</h2>
        <Link to="/invoices" className="text-brand-purple hover:underline mt-4 inline-block">Retour à la liste</Link>
      </div>
    );
  }

  const total = invoice.items.reduce((sum, item) => sum + item.quantity * item.unitPrice, 0);

  const handleStatusChange = (status: InvoiceStatus) => {
    if(id) {
        updateInvoiceStatus(id, status);
    }
  }

  return (
    <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Facture {invoice.invoiceNumber}</h1>
            <div className="flex items-center gap-2">
                <Link to={`/edit/${invoice.id}`} className="flex items-center bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg transition-colors duration-200">
                    <PencilIcon className="h-5 w-5 mr-2" />
                    Modifier
                </Link>
                {invoice.status === InvoiceStatus.Sent && (
                    <button onClick={() => handleStatusChange(InvoiceStatus.Paid)} className="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded-lg transition-colors duration-200">
                        Marquer comme Payée
                    </button>
                )}
                 {invoice.status === InvoiceStatus.Draft && (
                    <button onClick={() => handleStatusChange(InvoiceStatus.Sent)} className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-lg transition-colors duration-200">
                        Marquer comme Envoyée
                    </button>
                )}
            </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
            <div className="grid grid-cols-2 gap-8 mb-8">
                <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">De:</h3>
                    <p className="text-gray-600 dark:text-gray-400">Mon Entreprise</p>
                    <p className="text-gray-600 dark:text-gray-400">123 Ma Rue, Ma Ville</p>
                    <p className="text-gray-600 dark:text-gray-400"><EMAIL></p>
                </div>
                <div className="text-right">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Pour:</h3>
                    <p className="text-gray-600 dark:text-gray-400">{invoice.client.name}</p>
                    <p className="text-gray-600 dark:text-gray-400">{invoice.client.address}</p>
                    <p className="text-gray-600 dark:text-gray-400">{invoice.client.email}</p>
                </div>
            </div>

            <div className="grid grid-cols-3 gap-8 mb-8">
                 <div>
                    <h4 className="text-sm text-gray-600 dark:text-gray-400">Numéro de facture</h4>
                    <p className="text-gray-900 dark:text-white font-semibold">{invoice.invoiceNumber}</p>
                </div>
                 <div>
                    <h4 className="text-sm text-gray-600 dark:text-gray-400">Date d'émission</h4>
                    <p className="text-gray-900 dark:text-white font-semibold">{new Date(invoice.issueDate).toLocaleDateString('fr-FR')}</p>
                </div>
                 <div>
                    <h4 className="text-sm text-gray-600 dark:text-gray-400">Date d'échéance</h4>
                    <p className="text-gray-900 dark:text-white font-semibold">{new Date(invoice.dueDate).toLocaleDateString('fr-FR')}</p>
                </div>
            </div>

            <table className="w-full text-sm text-left text-gray-700 dark:text-gray-400 mb-8">
                <thead className="text-xs text-gray-600 dark:text-gray-400 uppercase bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th className="p-4">Description</th>
                        <th className="p-4 text-center">Qté</th>
                        <th className="p-4 text-right">Prix Unitaire</th>
                        <th className="p-4 text-right">Total</th>
                    </tr>
                </thead>
                <tbody>
                    {invoice.items.map(item => (
                         <tr key={item.id} className="border-b border-gray-200 dark:border-gray-700">
                             <td className="p-4 font-medium text-gray-900 dark:text-white">{item.description}</td>
                             <td className="p-4 text-center text-gray-700 dark:text-gray-400">{item.quantity}</td>
                             <td className="p-4 text-right font-mono text-gray-700 dark:text-gray-400">{item.unitPrice.toLocaleString('fr-MA', { style: 'currency', currency: 'MAD' })}</td>
                             <td className="p-4 text-right font-mono text-gray-900 dark:text-white">{(item.quantity * item.unitPrice).toLocaleString('fr-MA', { style: 'currency', currency: 'MAD' })}</td>
                         </tr>
                    ))}
                </tbody>
            </table>

            <div className="text-right">
                <p className="text-gray-600 dark:text-gray-400">Sous-total: <span className="text-gray-900 dark:text-white font-mono ml-4">{total.toLocaleString('fr-MA', { style: 'currency', currency: 'MAD' })}</span></p>
                <p className="text-gray-600 dark:text-gray-400 mt-2">TVA (20%): <span className="text-gray-900 dark:text-white font-mono ml-4">{(total * 0.2).toLocaleString('fr-MA', { style: 'currency', currency: 'MAD' })}</span></p>
                <p className="text-2xl text-gray-900 dark:text-white font-bold mt-4">Total: <span className="text-brand-purple font-mono ml-4">{(total * 1.2).toLocaleString('fr-MA', { style: 'currency', currency: 'MAD' })}</span></p>
            </div>
        </div>
    </div>
  );
};

export default InvoiceDetail;
