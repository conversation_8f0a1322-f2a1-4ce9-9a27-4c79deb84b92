import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useInvoices } from '../context/InvoiceContext';
import { Invoice, InvoiceStatus } from '../types';
import { TrashIcon } from '../components/Icons';
import ConfirmationDialog from '../components/ConfirmationDialog';

const getStatusClass = (status: InvoiceStatus) => {
  switch (status) {
    case InvoiceStatus.Paid: return 'bg-green-100 dark:bg-green-500/20 text-green-800 dark:text-green-400';
    case InvoiceStatus.Sent: return 'bg-yellow-100 dark:bg-yellow-500/20 text-yellow-800 dark:text-yellow-400';
    case InvoiceStatus.Overdue: return 'bg-red-100 dark:bg-red-500/20 text-red-800 dark:text-red-400';
    case InvoiceStatus.Draft: return 'bg-gray-100 dark:bg-gray-500/20 text-gray-800 dark:text-gray-400';
    default: return 'bg-gray-100 dark:bg-gray-500/20 text-gray-800 dark:text-gray-400';
  }
};

const InvoiceRow: React.FC<{ invoice: Invoice; onDelete: (id: string) => void; }> = ({ invoice, onDelete }) => {
  const total = invoice.items.reduce((sum, item) => sum + item.quantity * item.unitPrice, 0);

  return (
    <tr className="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200">
      <td className="p-4 font-medium text-gray-900 dark:text-white"><Link to={`/invoices/${invoice.id}`} className="hover:underline text-brand-purple">{invoice.invoiceNumber}</Link></td>
      <td className="p-4 text-gray-700 dark:text-gray-400">{invoice.client.name}</td>
      <td className="p-4 text-gray-700 dark:text-gray-400">{new Date(invoice.dueDate).toLocaleDateString('fr-FR')}</td>
      <td className="p-4 font-mono text-right text-gray-900 dark:text-white">{total.toLocaleString('fr-FR', { style: 'currency', currency: 'EUR' })}</td>
      <td className="p-4 text-center">
        <span className={`px-3 py-1 text-xs font-semibold rounded-full ${getStatusClass(invoice.status)}`}>
          {invoice.status}
        </span>
      </td>
      <td className="p-4 text-center">
        <button onClick={() => onDelete(invoice.id)} className="text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-500 p-2 rounded-full transition-colors duration-200" aria-label={`Supprimer la facture ${invoice.invoiceNumber}`}>
            <TrashIcon className="h-5 w-5" />
        </button>
      </td>
    </tr>
  );
};

const InvoiceList: React.FC = () => {
  const { invoices, deleteInvoice } = useInvoices();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [invoiceToDelete, setInvoiceToDelete] = useState<string | null>(null);

  const handleOpenDialog = (id: string) => {
    setInvoiceToDelete(id);
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setInvoiceToDelete(null);
    setIsDialogOpen(false);
  };

  const handleConfirmDelete = () => {
    if (invoiceToDelete) {
      deleteInvoice(invoiceToDelete);
      handleCloseDialog();
    }
  };


  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Factures</h1>
        <Link to="/create" className="bg-brand-purple hover:bg-brand-purple-light text-white font-bold py-2 px-4 rounded-lg transition-colors duration-200">
          Créer une Facture
        </Link>
      </div>
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full text-sm text-left text-gray-700 dark:text-gray-400">
            <thead className="text-xs text-gray-600 dark:text-gray-400 uppercase bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" className="p-4">Numéro</th>
                <th scope="col" className="p-4">Client</th>
                <th scope="col" className="p-4">Échéance</th>
                <th scope="col" className="p-4 text-right">Montant</th>
                <th scope="col" className="p-4 text-center">Statut</th>
                <th scope="col" className="p-4 text-center">Actions</th>
              </tr>
            </thead>
            <tbody>
              {invoices.map(invoice => (
                <InvoiceRow key={invoice.id} invoice={invoice} onDelete={handleOpenDialog} />
              ))}
            </tbody>
          </table>
        </div>
      </div>
       <ConfirmationDialog
        isOpen={isDialogOpen}
        onClose={handleCloseDialog}
        onConfirm={handleConfirmDelete}
        title="Confirmer la suppression"
      >
        <p>Êtes-vous sûr de vouloir supprimer cette facture ? Cette action est irréversible.</p>
      </ConfirmationDialog>
    </div>
  );
};

export default InvoiceList;