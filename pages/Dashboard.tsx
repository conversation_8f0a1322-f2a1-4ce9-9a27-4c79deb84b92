
import React from 'react';
import { useInvoices } from '../context/InvoiceContext';
import { InvoiceStatus } from '../types';

const StatCard: React.FC<{ title: string; value: string; color: string }> = ({ title, value, color }) => (
    <div className="bg-white p-6 rounded-lg shadow-lg border border-gray-200">
        <h3 className="text-gray-600 text-sm font-medium">{title}</h3>
        <p className={`text-3xl font-bold mt-2 ${color}`}>{value}</p>
    </div>
);

const Dashboard: React.FC = () => {
  const { invoices } = useInvoices();

  const calculateTotal = (status?: InvoiceStatus) => {
    return invoices
      .filter(inv => !status || inv.status === status)
      .reduce((sum, inv) => sum + inv.items.reduce((itemSum, item) => itemSum + item.quantity * item.unitPrice, 0), 0)
      .toLocaleString('fr-FR', { style: 'currency', currency: 'EUR' });
  };
  
  const countInvoices = (status: InvoiceStatus) => {
      return invoices.filter(inv => inv.status === status).length;
  }

  return (
    <div>
      <h1 className="text-3xl font-bold text-gray-900 mb-6">Tableau de bord</h1>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard title="Total Facturé" value={calculateTotal()} color="text-brand-purple" />
        <StatCard title="Total Payé" value={calculateTotal(InvoiceStatus.Paid)} color="text-green-600" />
        <StatCard title="En Attente" value={calculateTotal(InvoiceStatus.Sent)} color="text-yellow-600" />
        <StatCard title="En Retard" value={calculateTotal(InvoiceStatus.Overdue)} color="text-red-600" />
      </div>

      <div className="mt-8 bg-white p-6 rounded-lg shadow-lg border border-gray-200">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Aperçu des Factures</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
                <p className="text-4xl font-bold text-blue-600">{countInvoices(InvoiceStatus.Draft)}</p>
                <p className="text-gray-600">Brouillons</p>
            </div>
             <div>
                <p className="text-4xl font-bold text-yellow-600">{countInvoices(InvoiceStatus.Sent)}</p>
                <p className="text-gray-600">Envoyées</p>
            </div>
             <div>
                <p className="text-4xl font-bold text-green-600">{countInvoices(InvoiceStatus.Paid)}</p>
                <p className="text-gray-600">Payées</p>
            </div>
             <div>
                <p className="text-4xl font-bold text-red-600">{countInvoices(InvoiceStatus.Overdue)}</p>
                <p className="text-gray-600">En retard</p>
            </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
