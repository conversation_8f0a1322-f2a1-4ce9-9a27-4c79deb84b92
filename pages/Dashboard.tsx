
import React from 'react';
import { useInvoices } from '../context/InvoiceContext';
import { InvoiceStatus } from '../types';

const StatCard: React.FC<{ title: string; value: string; color: string }> = ({ title, value, color }) => (
    <div className="bg-gray-800 p-6 rounded-lg shadow-lg">
        <h3 className="text-gray-400 text-sm font-medium">{title}</h3>
        <p className={`text-3xl font-bold mt-2 ${color}`}>{value}</p>
    </div>
);

const Dashboard: React.FC = () => {
  const { invoices } = useInvoices();

  const calculateTotal = (status?: InvoiceStatus) => {
    return invoices
      .filter(inv => !status || inv.status === status)
      .reduce((sum, inv) => sum + inv.items.reduce((itemSum, item) => itemSum + item.quantity * item.unitPrice, 0), 0)
      .toLocaleString('fr-FR', { style: 'currency', currency: 'EUR' });
  };
  
  const countInvoices = (status: InvoiceStatus) => {
      return invoices.filter(inv => inv.status === status).length;
  }

  return (
    <div>
      <h1 className="text-3xl font-bold text-white mb-6">Tableau de bord</h1>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard title="Total Facturé" value={calculateTotal()} color="text-brand-purple" />
        <StatCard title="Total Payé" value={calculateTotal(InvoiceStatus.Paid)} color="text-green-400" />
        <StatCard title="En Attente" value={calculateTotal(InvoiceStatus.Sent)} color="text-yellow-400" />
        <StatCard title="En Retard" value={calculateTotal(InvoiceStatus.Overdue)} color="text-red-400" />
      </div>

      <div className="mt-8 bg-gray-800 p-6 rounded-lg shadow-lg">
        <h2 className="text-xl font-bold text-white mb-4">Aperçu des Factures</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
                <p className="text-4xl font-bold text-blue-400">{countInvoices(InvoiceStatus.Draft)}</p>
                <p className="text-gray-400">Brouillons</p>
            </div>
             <div>
                <p className="text-4xl font-bold text-yellow-400">{countInvoices(InvoiceStatus.Sent)}</p>
                <p className="text-gray-400">Envoyées</p>
            </div>
             <div>
                <p className="text-4xl font-bold text-green-400">{countInvoices(InvoiceStatus.Paid)}</p>
                <p className="text-gray-400">Payées</p>
            </div>
             <div>
                <p className="text-4xl font-bold text-red-400">{countInvoices(InvoiceStatus.Overdue)}</p>
                <p className="text-gray-400">En retard</p>
            </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
