
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useInvoices } from '../context/InvoiceContext';
import { Invoice, LineItem, InvoiceStatus } from '../types';
import { TrashIcon } from '../components/Icons';

const InvoiceForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { getInvoice, addInvoice, updateInvoice } = useInvoices();
  const isEditing = Boolean(id);
  
  const [formData, setFormData] = useState<Omit<Invoice, 'id' | 'invoiceNumber'>>({
    client: { name: '', email: '', address: '' },
    issueDate: new Date().toISOString().split('T')[0],
    dueDate: '',
    items: [{ id: `item-${Date.now()}`, description: '', quantity: 1, unitPrice: 0 }],
    status: InvoiceStatus.Draft,
  });

  useEffect(() => {
    if (isEditing && id) {
      const invoiceToEdit = getInvoice(id);
      if (invoiceToEdit) {
        setFormData(invoiceToEdit);
      }
    }
  }, [id, isEditing, getInvoice]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleClientChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, client: { ...prev.client, [name]: value } }));
  };

  const handleItemChange = (index: number, e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const newItems = [...formData.items];
    const itemToUpdate = newItems[index] as any;
    itemToUpdate[name] = name === 'description' ? value : parseFloat(value);
    setFormData(prev => ({ ...prev, items: newItems }));
  };

  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, { id: `item-${Date.now()}`, description: '', quantity: 1, unitPrice: 0 }],
    }));
  };

  const removeItem = (index: number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index),
    }));
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (isEditing && id) {
      updateInvoice(id, formData as Invoice);
      navigate(`/invoices/${id}`);
    } else {
      addInvoice(formData);
      navigate('/invoices');
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">{isEditing ? 'Modifier la Facture' : 'Créer une Facture'}</h1>
      <form onSubmit={handleSubmit} className="space-y-8">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Informations Client</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <input type="text" name="name" placeholder="Nom du client" value={formData.client.name} onChange={handleClientChange} className="bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white p-2 rounded border border-gray-300 dark:border-gray-600 focus:border-brand-purple focus:ring-1 focus:ring-brand-purple" required/>
            <input type="email" name="email" placeholder="Email du client" value={formData.client.email} onChange={handleClientChange} className="bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white p-2 rounded border border-gray-300 dark:border-gray-600 focus:border-brand-purple focus:ring-1 focus:ring-brand-purple" required/>
            <input type="text" name="address" placeholder="Adresse du client" value={formData.client.address} onChange={handleClientChange} className="bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white p-2 rounded border border-gray-300 dark:border-gray-600 focus:border-brand-purple focus:ring-1 focus:ring-brand-purple col-span-2" required/>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Détails de la Facture</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <input type="date" name="issueDate" value={formData.issueDate} onChange={handleChange} className="bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white p-2 rounded border border-gray-300 dark:border-gray-600 focus:border-brand-purple focus:ring-1 focus:ring-brand-purple" required/>
            <input type="date" name="dueDate" value={formData.dueDate} onChange={handleChange} className="bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white p-2 rounded border border-gray-300 dark:border-gray-600 focus:border-brand-purple focus:ring-1 focus:ring-brand-purple" required/>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Lignes de Facturation</h2>
          {formData.items.map((item, index) => (
            <div key={item.id} className="grid grid-cols-12 gap-2 mb-2 items-center">
              <input type="text" name="description" placeholder="Description" value={item.description} onChange={e => handleItemChange(index, e)} className="col-span-6 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white p-2 rounded border border-gray-300 dark:border-gray-600 focus:border-brand-purple focus:ring-1 focus:ring-brand-purple" required/>
              <input type="number" name="quantity" placeholder="Qté" value={item.quantity} onChange={e => handleItemChange(index, e)} className="col-span-2 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white p-2 rounded border border-gray-300 dark:border-gray-600 focus:border-brand-purple focus:ring-1 focus:ring-brand-purple" min="1" required/>
              <input type="number" name="unitPrice" placeholder="Prix" value={item.unitPrice} onChange={e => handleItemChange(index, e)} className="col-span-3 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white p-2 rounded border border-gray-300 dark:border-gray-600 focus:border-brand-purple focus:ring-1 focus:ring-brand-purple" min="0" step="0.01" required/>
              <button type="button" onClick={() => removeItem(index)} className="col-span-1 text-red-600 dark:text-red-500 hover:text-red-800 dark:hover:text-red-400">
                <TrashIcon className="h-6 w-6"/>
              </button>
            </div>
          ))}
          <button type="button" onClick={addItem} className="mt-4 text-brand-purple font-semibold hover:text-brand-purple-light">Ajouter une ligne</button>
        </div>

        <div className="flex justify-end gap-4">
          <button type="button" onClick={() => navigate(-1)} className="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-6 rounded-lg">Annuler</button>
          <button type="submit" className="bg-brand-purple hover:bg-brand-purple-light text-white font-bold py-2 px-6 rounded-lg">{isEditing ? 'Sauvegarder' : 'Créer'}</button>
        </div>
      </form>
    </div>
  );
};

export default InvoiceForm;
