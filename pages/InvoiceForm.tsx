
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useInvoices } from '../context/InvoiceContext';
import { Invoice, LineItem, InvoiceStatus } from '../types';
import { TrashIcon } from '../components/Icons';

const InvoiceForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { getInvoice, addInvoice, updateInvoice } = useInvoices();
  const isEditing = Boolean(id);
  
  const [formData, setFormData] = useState<Omit<Invoice, 'id' | 'invoiceNumber'>>({
    client: { name: '', email: '', address: '' },
    issueDate: new Date().toISOString().split('T')[0],
    dueDate: '',
    items: [{ id: `item-${Date.now()}`, description: '', quantity: 1, unitPrice: 0 }],
    status: InvoiceStatus.Draft,
  });

  useEffect(() => {
    if (isEditing && id) {
      const invoiceToEdit = getInvoice(id);
      if (invoiceToEdit) {
        setFormData(invoiceToEdit);
      }
    }
  }, [id, isEditing, getInvoice]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleClientChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, client: { ...prev.client, [name]: value } }));
  };

  const handleItemChange = (index: number, e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const newItems = [...formData.items];
    const itemToUpdate = newItems[index] as any;
    itemToUpdate[name] = name === 'description' ? value : parseFloat(value);
    setFormData(prev => ({ ...prev, items: newItems }));
  };

  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, { id: `item-${Date.now()}`, description: '', quantity: 1, unitPrice: 0 }],
    }));
  };

  const removeItem = (index: number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index),
    }));
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (isEditing && id) {
      updateInvoice(id, formData as Invoice);
      navigate(`/invoices/${id}`);
    } else {
      addInvoice(formData);
      navigate('/invoices');
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">{isEditing ? 'Modifier la Facture' : 'Créer une Facture'}</h1>
      <form onSubmit={handleSubmit} className="space-y-8">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Informations Client</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Nom du client *</label>
              <input type="text" name="name" placeholder="Nom du client" value={formData.client.name} onChange={handleClientChange} className="w-full bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white p-3 rounded border border-gray-300 dark:border-gray-600 focus:border-gray-400 dark:focus:border-gray-500 focus:ring-1 focus:ring-gray-400 dark:focus:ring-gray-500" required/>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email du client *</label>
              <input type="email" name="email" placeholder="Email du client" value={formData.client.email} onChange={handleClientChange} className="w-full bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white p-3 rounded border border-gray-300 dark:border-gray-600 focus:border-gray-400 dark:focus:border-gray-500 focus:ring-1 focus:ring-gray-400 dark:focus:ring-gray-500" required/>
            </div>
            <div className="col-span-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Adresse du client *</label>
              <input type="text" name="address" placeholder="Adresse du client" value={formData.client.address} onChange={handleClientChange} className="w-full bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white p-3 rounded border border-gray-300 dark:border-gray-600 focus:border-gray-400 dark:focus:border-gray-500 focus:ring-1 focus:ring-gray-400 dark:focus:ring-gray-500" required/>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Détails de la Facture</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Date d'émission *</label>
              <input type="date" name="issueDate" value={formData.issueDate} onChange={handleChange} className="w-full bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-5 text-xl rounded border border-gray-300 dark:border-gray-600 focus:border-gray-400 dark:focus:border-gray-500 focus:ring-1 focus:ring-gray-400 dark:focus:ring-gray-500 date-input-large" required/>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Date d'échéance *</label>
              <input type="date" name="dueDate" value={formData.dueDate} onChange={handleChange} className="w-full bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-5 text-xl rounded border border-gray-300 dark:border-gray-600 focus:border-gray-400 dark:focus:border-gray-500 focus:ring-1 focus:ring-gray-400 dark:focus:ring-gray-500 date-input-large" required/>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Lignes de Facturation</h2>
          <div className="mb-4">
            <div className="grid grid-cols-12 gap-2 mb-3 text-sm font-medium text-gray-700 dark:text-gray-300">
              <div className="col-span-6">Description</div>
              <div className="col-span-2 text-center">Quantité</div>
              <div className="col-span-3 text-center">Prix unitaire (MAD)</div>
              <div className="col-span-1"></div>
            </div>
            {formData.items.map((item, index) => (
              <div key={item.id} className="grid grid-cols-12 gap-2 mb-3 items-center">
                <input type="text" name="description" placeholder="Description du service/produit" value={item.description} onChange={e => handleItemChange(index, e)} className="col-span-6 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white p-3 rounded border border-gray-300 dark:border-gray-600 focus:border-gray-400 dark:focus:border-gray-500 focus:ring-1 focus:ring-gray-400 dark:focus:ring-gray-500" required/>
                <input type="number" name="quantity" placeholder="1" value={item.quantity} onChange={e => handleItemChange(index, e)} className="col-span-2 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white p-3 rounded border border-gray-300 dark:border-gray-600 focus:border-gray-400 dark:focus:border-gray-500 focus:ring-1 focus:ring-gray-400 dark:focus:ring-gray-500 text-center" min="1" required/>
                <input type="number" name="unitPrice" placeholder="0.00" value={item.unitPrice} onChange={e => handleItemChange(index, e)} className="col-span-3 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white p-3 rounded border border-gray-300 dark:border-gray-600 focus:border-gray-400 dark:focus:border-gray-500 focus:ring-1 focus:ring-gray-400 dark:focus:ring-gray-500 text-right" min="0" step="0.01" required/>
                <button type="button" onClick={() => removeItem(index)} className="col-span-1 text-red-600 dark:text-red-500 hover:text-red-800 dark:hover:text-red-400 p-2 rounded-full hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors">
                  <TrashIcon className="h-5 w-5"/>
                </button>
              </div>
            ))}
          </div>
          <button type="button" onClick={addItem} className="mt-4 text-gray-600 dark:text-gray-400 font-semibold hover:text-gray-800 dark:hover:text-gray-200 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 px-4 py-2 rounded-lg transition-colors">+ Ajouter une ligne</button>
        </div>

        <div className="flex justify-end gap-4">
          <button type="button" onClick={() => navigate(-1)} className="bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-6 rounded-lg transition-colors">Annuler</button>
          <button type="submit" className="bg-brand-purple hover:bg-brand-purple-light text-white font-bold py-3 px-6 rounded-lg transition-colors">{isEditing ? 'Sauvegarder' : 'Créer'}</button>
        </div>
      </form>
    </div>
  );
};

export default InvoiceForm;
