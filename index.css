/* Base styles for the invoice manager application */
:root {
  --date-input-bg: rgb(249 250 251); /* gray-50 */
  --date-input-text: rgb(17 24 39); /* gray-900 */
  --date-input-border: rgb(209 213 219); /* gray-300 */
}

.dark {
  --date-input-bg: rgb(55 65 81); /* gray-700 */
  --date-input-text: rgb(255 255 255); /* white */
  --date-input-border: rgb(75 85 99); /* gray-600 */
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
}

/* Light mode scrollbar */
::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-track {
  background: #2d2d2d;
}

.dark ::-webkit-scrollbar-thumb {
  background: #3c3c3c;
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #4a4a4a;
}

/* Ensure full height */
html, body, #root {
  height: 100%;
}

/* Custom styles for date picker calendar */
input[type="date"]::-webkit-calendar-picker-indicator {
  background: transparent;
  bottom: 0;
  color: transparent;
  cursor: pointer;
  height: auto;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  width: auto;
  font-size: 18px;
}

/* Increase calendar size when opened */
input[type="date"]::-webkit-datetime-edit {
  font-size: 18px;
  padding: 8px;
}

input[type="date"]::-webkit-datetime-edit-fields-wrapper {
  font-size: 18px;
}

input[type="date"]::-webkit-datetime-edit-text {
  font-size: 18px;
  padding: 2px;
}

input[type="date"]::-webkit-datetime-edit-month-field,
input[type="date"]::-webkit-datetime-edit-day-field,
input[type="date"]::-webkit-datetime-edit-year-field {
  font-size: 18px;
  padding: 4px;
  min-width: 40px;
}

/* For Firefox */
input[type="date"] {
  font-size: 18px !important;
  min-height: 50px;
  color-scheme: light;
}

/* Firefox dark mode support */
.dark input[type="date"] {
  color-scheme: dark;
}

/* Dark mode calendar styles */
.dark input[type="date"]::-webkit-calendar-picker-indicator {
  filter: invert(1);
}

/* Large date input specific styles */
.date-input-large {
  font-size: 20px !important;
  min-height: 60px !important;
}

.date-input-large::-webkit-datetime-edit {
  font-size: 20px !important;
  padding: 10px !important;
}

.date-input-large::-webkit-datetime-edit-fields-wrapper {
  font-size: 20px !important;
}

.date-input-large::-webkit-datetime-edit-text {
  font-size: 20px !important;
  padding: 3px !important;
}

.date-input-large::-webkit-datetime-edit-month-field,
.date-input-large::-webkit-datetime-edit-day-field,
.date-input-large::-webkit-datetime-edit-year-field {
  font-size: 20px !important;
  padding: 6px !important;
  min-width: 50px !important;
}

.date-input-large::-webkit-calendar-picker-indicator {
  font-size: 24px !important;
  width: 30px !important;
  height: 30px !important;
}

/* Firefox specific calendar styling */
@-moz-document url-prefix() {
  input[type="date"] {
    background-color: var(--date-input-bg) !important;
    color: var(--date-input-text) !important;
    border-color: var(--date-input-border) !important;
  }

  /* Firefox calendar popup styling */
  input[type="date"]::-moz-calendar-picker {
    background-color: var(--date-input-bg) !important;
    color: var(--date-input-text) !important;
    border-color: var(--date-input-border) !important;
  }
}

/* Alternative approach for Firefox using attribute selectors */
input[type="date"][data-theme="light"] {
  color-scheme: light;
}

input[type="date"][data-theme="dark"] {
  color-scheme: dark;
}
