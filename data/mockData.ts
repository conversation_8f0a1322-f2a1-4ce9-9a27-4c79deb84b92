
import { Invoice, InvoiceStatus } from '../types';

export const MOCK_INVOICES: Invoice[] = [
  {
    id: 'inv-001',
    invoiceNumber: '2024-001',
    client: {
      name: 'Tech Solutions Inc.',
      address: '123 Rue de la Tech, 75001 Paris',
      email: '<EMAIL>',
    },
    issueDate: '2024-07-15',
    dueDate: '2024-08-14',
    items: [
      { id: 'item-1', description: 'Développement Web - T1', quantity: 40, unitPrice: 90 },
      { id: 'item-2', description: 'Hébergement (Annuel)', quantity: 1, unitPrice: 300 },
    ],
    status: InvoiceStatus.Paid,
  },
  {
    id: 'inv-002',
    invoiceNumber: '2024-002',
    client: {
      name: 'Créations Design',
      address: '456 Avenue des Arts, 69002 Lyon',
      email: '<EMAIL>',
    },
    issueDate: '2024-07-20',
    dueDate: '2024-08-19',
    items: [
      { id: 'item-3', description: 'Création de logo', quantity: 1, unitPrice: 1200 },
      { id: 'item-4', description: 'Charte graphique', quantity: 1, unitPrice: 2500 },
    ],
    status: InvoiceStatus.Sent,
  },
  {
    id: 'inv-003',
    invoiceNumber: '2024-003',
    client: {
      name: 'Gourmet Express',
      address: '789 Boulevard du Goût, 13006 Marseille',
      email: '<EMAIL>',
    },
    issueDate: '2024-06-01',
    dueDate: '2024-07-01',
    items: [
      { id: 'item-5', description: 'Consultation Marketing', quantity: 10, unitPrice: 150 },
    ],
    status: InvoiceStatus.Overdue,
  },
  {
    id: 'inv-004',
    invoiceNumber: '2024-004',
    client: {
      name: 'EcoBuild Constructeurs',
      address: '101 Rue de l\'Écologie, 31000 Toulouse',
      email: '<EMAIL>',
    },
    issueDate: '2024-07-28',
    dueDate: '2024-08-27',
    items: [
      { id: 'item-6', description: 'Étude de terrain', quantity: 1, unitPrice: 1800 },
      { id: 'item-7', description: 'Plan d\'architecte', quantity: 1, unitPrice: 4500 },
    ],
    status: InvoiceStatus.Draft,
  },
];
