
import React from 'react';
import { NavLink, Link } from 'react-router-dom';
import { DashboardIcon, DocumentTextIcon, PlusCircleIcon } from './Icons';

const Sidebar: React.FC = () => {
  const commonLinkClasses = "flex items-center px-4 py-3 text-gray-400 hover:bg-gray-700 hover:text-white transition-colors duration-200 rounded-lg";
  const activeLinkClasses = "bg-brand-purple text-white";

  return (
    <aside className="w-64 flex-shrink-0 bg-gray-800 p-4 hidden md:flex flex-col">
      <div className="text-white text-2xl font-bold mb-10 px-4">Factures Pro</div>
      <nav className="flex-1 space-y-2">
        <NavLink
          to="/"
          className={({ isActive }) => `${commonLinkClasses} ${isActive ? activeLinkClasses : ''}`}
        >
          <DashboardIcon className="h-6 w-6 mr-3" />
          Tableau de bord
        </NavLink>
        <NavLink
          to="/invoices"
          className={({ isActive }) => `${commonLinkClasses} ${isActive ? activeLinkClasses : ''}`}
        >
          <DocumentTextIcon className="h-6 w-6 mr-3" />
          Factures
        </NavLink>
      </nav>
      <div>
        <Link to="/create" className="flex w-full items-center justify-center bg-brand-purple hover:bg-brand-purple-light text-white font-bold py-3 px-4 rounded-lg transition-colors duration-200">
          <PlusCircleIcon className="h-6 w-6 mr-2"/>
          Nouvelle Facture
        </Link>
      </div>
    </aside>
  );
};

export default Sidebar;
