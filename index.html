<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Gestionnaire de Factures Pro</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              'gray-900': '#121212',
              'gray-800': '#1e1e1e',
              'gray-700': '#2d2d2d',
              'gray-600': '#3c3c3c',
              'gray-400': '#a0a0a0',
              'gray-200': '#e0e0e0',
              'brand-purple': '#8B5CF6',
              'brand-purple-light': '#A78BFA',
            }
          }
        }
      }
    </script>
  <script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@18.2.0",
    "react-dom/client": "https://esm.sh/react-dom@18.2.0/client",
    "react-router-dom": "https://esm.sh/react-router-dom@6.22.3",
    "react/": "https://aistudiocdn.com/react@^19.1.1/",
    "react-dom/": "https://aistudiocdn.com/react-dom@^19.1.1/"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
  <body class="bg-gray-900 text-gray-200">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>